import json
import re
import requests
from bs4 import BeautifulSoup


def getHTMLdocument(url):
    try:
        response = requests.get(url, timeout=10)
        response.raise_for_status()
        return response.text
    except requests.RequestException as e:
        print(f"Error fetching {url}: {e}")
        return ""


with open("INFO/data.json") as file:
    data = json.load(file)
    for i in data:
        url_to_scrape = i["URL"]
        html_document = getHTMLdocument(url_to_scrape)
        soup = BeautifulSoup(html_document, 'html.parser')
        print(soup)
