import json
import time
from selenium.common import TimeoutException
from selenium.webdriver import Keys
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from configs.config_data import WAIT, MINI_WAIT, SITE_LINK, FIL<PERSON>, FILE_PATH, INFO_PATH, DATA_PATH
from functions import make_dir


class Basis:
    def __init__(self, driver):
        self.wait = WAIT
        self.miniwait = MINI_WAIT
        self.site_link = SITE_LINK
        self.driver = driver
        self.info_path = INFO_PATH
        self.data_path = DATA_PATH
        self.file = FILE
        self.data = FILE_PATH
        self.item = []
        self.data_list = []
        self.run_scraper()

    def run_scraper(self):
        self.open_web_page()
        self.get_product_link()
        self.save_data()

    def open_link_new_tab(self, link):
        self.driver.execute_script("window.open('');")
        self.driver.switch_to.window(self.driver.window_handles[1])
        self.driver.get(link)
        time.sleep(1)
        WebDriverWait(self.driver, self.wait).until(
            lambda driver: driver.execute_script("return document.readyState") == "complete"
        )

    def close_new_tab(self):
        self.driver.close()
        self.driver.switch_to.window(self.driver.window_handles[0])

    def open_web_page(self):
        self.driver.get(self.site_link)

    def get_product_link(self):
        visited_pages = set()
        while True:
            try:
                self.driver.find_element(By.TAG_NAME, 'body').send_keys(Keys.END)
                time.sleep(2)
                page_buttons = self.driver.find_elements(By.XPATH,'//ul[contains(@class,"pagination")]/li/a[@class="page-link"]')
                for btn in page_buttons:
                    page_num = btn.text.strip()
                    if page_num not in visited_pages and page_num.isdigit():
                        visited_pages.add(page_num)
                        self.driver.find_element(By.TAG_NAME, 'body').send_keys(Keys.END)
                        time.sleep(1)
                        btn.click()
                        print(f"Visited page {page_num}")
                        time.sleep(1)
                        self.get_product_link_list()
                        break
                else:
                    print("All pages visited.")
                    break
            except Exception as e:
                print(f"Error while clicking pages: {e}")
                break

    def get_product_link_list(self):
        try:
            time.sleep(1)
            link_selectors = [
                '//div[@class="search-list-item row mb-3"]/div[3]/h4/a',
                '//div[contains(@class, "search-list-item")]//a[contains(@href, "company-profile")]',
                '//a[contains(@href, "company-profile")]',
                '//div[contains(@class, "company")]//a',
                '//h4/a[contains(@href, "company-profile")]'
            ]
            elements = None
            for selector in link_selectors:
                try:
                    print(f"Trying link selector: {selector}")
                    elements = WebDriverWait(self.driver, self.wait).until(
                        EC.presence_of_all_elements_located((By.XPATH, selector))
                    )
                    if elements:
                        print(f"Found {len(elements)} links with selector: {selector}")
                        break
                except Exception as e:
                    print(f"Link selector failed ({selector}): {e}")
                    continue
            if not elements:
                print("No company links found with any selector")
                return
            for element in elements:
                link = element.get_attribute('href')
                print(f"Processing link: {link}")
                self.open_link_new_tab(link)
                self.extract_data(link)
                self.close_new_tab()
        except Exception as e:
            print(f"Error on 'get_product_link_list()' - {e}")

    def extract_data(self, link):
        item = {}
        print(f"Extracting data from: {link}")
        time.sleep(1)
        self.driver.execute_script("return document.readyState") == "complete"
        try:
            WebDriverWait(self.driver, self.wait).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            print("Page body loaded")
        except TimeoutException:
            print("Timeout waiting for page body")
        company_selectors = [
            '//div[@class="companyDetails"]/h1',
            '//h1[contains(@class, "company")]',
            '//div[contains(@class, "company")]/h1',
            '//h1',
            '//div[@class="company-name"]',
            '//div[contains(@class, "profile")]/h1'
        ]
        item["Company"] = self.extract_text_with_multiple_selectors(company_selectors, "Company name")
        location_selectors = [
            '//div[@class="companyDetails"]/p',
            '//div[contains(@class, "location")]',
            '//p[contains(@class, "address")]',
            '//div[contains(@class, "address")]',
            '//div[@class="companyDetails"]//p'
        ]
        item["location"] = self.extract_text_with_multiple_selectors(location_selectors, "Location")
        phone_selectors = [
            '//strong[text()="Phone:"]/parent::*',
            '//strong[contains(text(), "Phone")]/parent::*',
            '//span[contains(text(), "Phone")]/parent::*',
            '//div[contains(text(), "Phone")]',
            '//p[contains(text(), "Phone")]',
            '//*[contains(text(), "Phone:")]'
        ]
        phone_text = self.extract_text_with_multiple_selectors(phone_selectors, "Phone")
        item["Phone"] = phone_text.replace("Phone:", "").replace("phone:", "").strip() if phone_text != "not found" else "not found"
        email_selectors = [
            '//strong[text()="Email:"]/parent::*',
            '//strong[contains(text(), "Email")]/parent::*',
            '//span[contains(text(), "Email")]/parent::*',
            '//div[contains(text(), "Email")]',
            '//p[contains(text(), "Email")]',
            '//*[contains(text(), "Email:")]',
            '//a[contains(@href, "mailto:")]'
        ]

        email_text = self.extract_text_with_multiple_selectors(email_selectors, "Email")
        if email_text != "not found":
            if "mailto:" in email_text:
                import re
                email_match = re.search(r'mailto:([^"\'>\s]+)', email_text)
                item["Email"] = email_match.group(1) if email_match else email_text.replace("Email:", "").strip()
            else:
                item["Email"] = email_text.replace("Email:", "").replace("email:", "").strip()
        else:
            item["Email"] = "not found"
        item['Source'] = "Basis"
        item["URL"] = link
        self.item.append(item)
        print(f"Extracted item: {item}")

    def extract_text_with_multiple_selectors(self, selectors, field_name):
        for selector in selectors:
            try:
                print(f"Trying selector for {field_name}: {selector}")
                element = WebDriverWait(self.driver, self.miniwait).until(
                    EC.presence_of_element_located((By.XPATH, selector))
                )
                text = element.text.strip()
                if text:
                    print(f"Found {field_name}: {text}")
                    return text
                else:
                    print(f"Element found but text is empty for selector: {selector}")
            except Exception as e:
                print(f"Selector failed for {field_name} ({selector}): {e}")
                continue
        print(f"No working selector found for {field_name}")
        return "not found"

    def save_data(self):
        make_dir(f"{self.info_path}")
        with open(f"{self.info_path}/{self.file}", 'w') as file:
            json.dump(self.item, file, indent=4)
            print("Data saved successfully!")
