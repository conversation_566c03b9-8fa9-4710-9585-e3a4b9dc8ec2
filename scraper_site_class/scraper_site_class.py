import json
import time
from selenium.common import TimeoutException
from selenium.webdriver import Keys
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from configs.config_data import WAIT, MINI_WAIT, SITE_LINK, FIL<PERSON>, FILE_PATH, INFO_PATH, DATA_PATH
from functions import make_dir


class Basis:
    def __init__(self, driver):
        self.wait = WAIT
        self.miniwait = MINI_WAIT
        self.site_link = SITE_LINK
        self.driver = driver
        self.info_path = INFO_PATH
        self.data_path = DATA_PATH
        self.file = FILE
        self.data = FILE_PATH
        self.item = []
        self.data_list = []
        self.run_scraper()

    def run_scraper(self):
        self.open_web_page()
        self.get_product_link()
        self.save_data()

    def open_link_new_tab(self, link):
        self.driver.execute_script("window.open('');")
        self.driver.switch_to.window(self.driver.window_handles[1])
        self.driver.get(link)

    def close_new_tab(self):
        self.driver.close()
        self.driver.switch_to.window(self.driver.window_handles[0])

    def open_web_page(self):
        self.driver.get(self.site_link)

    def get_product_link(self):
        visited_pages = set()
        while True:
            try:
                self.driver.find_element(By.TAG_NAME, 'body').send_keys(Keys.END)
                time.sleep(2)
                page_buttons = self.driver.find_elements(By.XPATH,'//ul[contains(@class,"pagination")]/li/a[@class="page-link"]')
                for btn in page_buttons:
                    page_num = btn.text.strip()
                    if page_num not in visited_pages and page_num.isdigit():
                        visited_pages.add(page_num)
                        self.driver.find_element(By.TAG_NAME, 'body').send_keys(Keys.END)
                        time.sleep(1)
                        btn.click()
                        print(f"Visited page {page_num}")
                        time.sleep(3)
                        self.get_product_link_list()
                        break
                else:
                    print("All pages visited.")
                    break
            except Exception as e:
                print(f"Error while clicking pages: {e}")
                break

    def get_product_link_list(self):
        try:
            elements = WebDriverWait(self.driver, self.wait).until(
                EC.presence_of_all_elements_located(
                    (By.XPATH,
                     '//div[@class="search-list-item row mb-3"]/div[3]/h4/a'))
            )
            for element in elements:
                link = element.get_attribute('href')
                print(link)
                self.open_link_new_tab(link)
                self.extract_data(link)
                self.close_new_tab()
        except Exception as e:
            print(f"Error on 'get_category_list()' - {e}")

    def extract_data(self, link):
        item = {}
        try:
            element = WebDriverWait(self.driver, self.miniwait).until(
                EC.presence_of_element_located(
                    (By.XPATH,
                     '//div[@class="companyDetails"]/h1'))
            )
            item["Company"] = element.text
        except:
            item["Company"] = "not found"

        try:
            element = WebDriverWait(self.driver, self.miniwait).until(
                EC.presence_of_element_located(
                    (By.XPATH,
                     '//div[@class="companyDetails"]/p'))
            )
            item["location"] = element.text
        except:
            item["location"] = "not found"

        try:
            element = WebDriverWait(self.driver, self.miniwait).until(
                EC.presence_of_element_located(
                    (By.XPATH, '//strong[text()="Phone:"]/parent::*')
                )
            )
            full_text = element.text
            item["Phone"] = full_text.replace("Phone:", "").strip()
        except:
            item["Phone"] = "not found"

        try:
            element = WebDriverWait(self.driver, self.miniwait).until(
                EC.presence_of_element_located(
                    (By.XPATH, '//strong[text()="Email:"]/parent::*')
                )
            )
            full_text = element.text
            item["Email"] = full_text.replace("Email:", "").strip()
        except:
            item["Email"] = "not found"

        item['Source'] = "Basis"
        item["URL"] = link
        self.item.append(item)
        print(item)

    def save_data(self):
        make_dir(f"{self.info_path}")
        with open(f"{self.info_path}/{self.file}", 'w') as file:
            json.dump(self.item, file, indent=4)
            print("Data saved successfully!")
