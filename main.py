from selenium import webdriver
import chromedriver_autoinstaller
from selenium.webdriver.chrome.options import Options

from configs.config_data import ROOT_PATH, DATA_PATH, DRIVER_PATH, INFO_PATH
from functions import make_dir
from scraper_site_class.scraper_site_class import Basis

driver_path = f"{ROOT_PATH}/{DATA_PATH}/{DRIVER_PATH}"
make_dir(f"{ROOT_PATH}/{DATA_PATH}")
make_dir(driver_path)

options = Options()
options.headless = False
# Add options to better handle JavaScript
options.add_argument('--disable-blink-features=AutomationControlled')
options.add_experimental_option("excludeSwitches", ["enable-automation"])
options.add_experimental_option('useAutomationExtension', False)
options.add_argument('--no-sandbox')
options.add_argument('--disable-dev-shm-usage')

chromedriver_autoinstaller.install(path=driver_path)
driver = webdriver.Chrome(options=options)
# Disable automation detection
driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
driver.maximize_window()

# Disable test mode to scrape all companies
scraper = Basis(driver, test_mode=False)
